---
title: "<PERSON><PERSON> Console"
---

Envoy Gateway provides a built-in web-based admin console for monitoring and debugging the control plane. The admin console offers a user-friendly interface to access server information, configuration dumps, performance metrics, and debugging tools.

## Prerequisites

{{< boilerplate o11y_prerequisites >}}

## Overview

The admin console is always enabled and provides access to:

- **Server Status**: Real-time information about Envoy Gateway components and their health
- **Configuration Dump**: Current state of Gateway API resources and policies
- **Performance Metrics**: Control plane metrics in Prometheus format
- **Performance Profiling**: pprof endpoints for debugging (when enabled)

## Accessing the Admin Console

### Using egctl (Recommended)

The easiest way to access the admin console is using the `egctl` command:

```shell
egctl x dashboard envoy-gateway
```

This command will:
1. Automatically find the Envoy Gateway pod
2. Set up port forwarding to the admin server
3. Open your browser to the admin console

You can also specify a custom namespace or port:

```shell
# Use custom namespace
egctl x dashboard envoy-gateway -n custom-namespace

# Use custom local port
egctl x dashboard envoy-gateway --port 8080
```

### Manual Port Forwarding

Alternatively, you can manually set up port forwarding:

```shell
export ENVOY_POD_NAME=$(kubectl get pod -n envoy-gateway-system --selector=control-plane=envoy-gateway -o jsonpath='{.items[0].metadata.name}')
kubectl port-forward pod/$ENVOY_POD_NAME -n envoy-gateway-system 19000:19000
```

Then open your browser to `http://localhost:19000`.

## Admin Console Features

### Server Information

The server information page displays:

- **System Information**: Version, uptime, Go version, and platform details
- **Component Status**: Status of core components (Provider Service, Gateway API Translator, xDS Translator, Infrastructure Manager)
- **EnvoyGateway Configuration**: Current configuration loaded from ConfigMap or defaults

### Configuration Dump

The configuration dump page provides:

- **Resource Summary**: Overview of all Gateway API resources and policies
- **Detailed View**: Complete configuration data in JSON format (accessible via `resource=all` parameter)
- **Resource Categories**:
  - Gateway API Core Resources (Gateways, HTTPRoutes, etc.)
  - Envoy Gateway Policies (ClientTrafficPolicy, SecurityPolicy, etc.)
  - Kubernetes Resources (Services, Secrets, ConfigMaps, etc.)

### Statistics

The statistics page offers:

- **Control Plane Metrics**: All Envoy Gateway metrics in Prometheus format
- **Metrics Categories**: Watching Components, Status Updater, xDS Server, Infrastructure Manager, Wasm, and Topology Injector

{{% alert title="Exported Metrics" color="warning" %}}
Refer to the [Gateway Exported Metrics List](./gateway-exported-metrics) to learn more about available metrics.
{{% /alert %}}

### Performance Profiling

When enabled, the profiling page provides access to:

- **CPU Profile**: CPU usage analysis
- **Memory Heap**: Memory allocation patterns
- **Goroutines**: Stack traces of all goroutines
- **Mutex/Block**: Contention analysis

## Configuration

### Basic Configuration

The admin console is always enabled with default settings. The default configuration uses:

- **Host**: `127.0.0.1` (localhost only)
- **Port**: `19000`
- **pprof**: Disabled (for security)

### Enabling Performance Profiling

To enable pprof endpoints for debugging, update your EnvoyGateway configuration:

{{< tabpane text=true >}}
{{% tab header="Apply from stdin" %}}

```shell
cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: ConfigMap
metadata:
  name: envoy-gateway-config
  namespace: envoy-gateway-system
data:
  envoy-gateway.yaml: |
    apiVersion: gateway.envoyproxy.io/v1alpha1
    kind: EnvoyGateway
    provider:
      type: Kubernetes
    gateway:
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
    admin:
      enablePprof: true
      address:
        host: "127.0.0.1"
        port: 19000
EOF
```

{{% /tab %}}
{{% tab header="Apply from file" %}}
Save and apply the following resource to your cluster:

```yaml
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: envoy-gateway-config
  namespace: envoy-gateway-system
data:
  envoy-gateway.yaml: |
    apiVersion: gateway.envoyproxy.io/v1alpha1
    kind: EnvoyGateway
    provider:
      type: Kubernetes
    gateway:
      controllerName: gateway.envoyproxy.io/gatewayclass-controller
    admin:
      enablePprof: true
      address:
        host: "127.0.0.1"
        port: 19000
```

{{% /tab %}}
{{< /tabpane >}}

{{< boilerplate rollout-envoy-gateway >}}

### Development Configuration

For development environments, you might want to enable all debugging features:

```yaml
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyGateway
admin:
  enablePprof: true
  enableDumpConfig: true
  address:
    host: "0.0.0.0"  # Allow external access (use with caution)
    port: 19000
logging:
  level:
    default: debug
```

{{% alert title="Security Warning" color="warning" %}}
Only enable external access (`host: "0.0.0.0"`) and pprof endpoints in development environments. In production, keep the default localhost-only access for security.
{{% /alert %}}

## API Endpoints

The admin console also exposes REST API endpoints for programmatic access:

| Endpoint | Description |
|----------|-------------|
| `/api/info` | Basic system information |
| `/api/server_info` | Detailed server status and components |
| `/api/config_dump` | Configuration summary (use `?resource=all` for complete data) |
| `/api/metrics` | Prometheus metrics |

Example usage:

```shell
# Get system information
curl http://localhost:19000/api/info

# Get server status
curl http://localhost:19000/api/server_info

# Get configuration summary
curl http://localhost:19000/api/config_dump

# Get complete configuration data
curl http://localhost:19000/api/config_dump?resource=all

# Get metrics
curl http://localhost:19000/api/metrics
```

## Troubleshooting

### Console Not Accessible

1. **Check pod status**:
   ```shell
   kubectl get pods -n envoy-gateway-system
   ```

2. **Verify port forwarding**:
   ```shell
   kubectl port-forward pod/$ENVOY_POD_NAME -n envoy-gateway-system 19000:19000
   ```

3. **Check admin configuration**:
   ```shell
   kubectl get configmap envoy-gateway-config -n envoy-gateway-system -o yaml
   ```

### Performance Issues

1. **Check resource usage**:
   ```shell
   kubectl top pods -n envoy-gateway-system
   ```

2. **Review logs**:
   ```shell
   kubectl logs -n envoy-gateway-system deployment/envoy-gateway
   ```

3. **Use profiling tools** (when pprof is enabled):
   ```shell
   go tool pprof http://localhost:19000/debug/pprof/profile
   ```
